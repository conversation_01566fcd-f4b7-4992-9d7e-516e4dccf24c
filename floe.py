from graphviz import Digraph

dot = Digraph(comment='Editing Control Flow')
dot.attr(rankdir='LR', fontsize='12', fontname='Helvetica')
dot.attr('node', shape='box', style='rounded')

# Nodes
dot.node('Start', 'Start: Application Created')
dot.node('StudentControl', 'Student Has Edit Control')
dot.node('AgentRequests', 'Agent Clicks "Take Control to Edit"')
dot.node('AgentControl', 'Agent Has Edit Control\n(48-hour lock starts)')
dot.node('StudentViewReadOnly', 'Student View: Read-only')
dot.node('AgentViewEditable', 'Agent View: Editable\n[Release for Student]')

dot.node('AgentReleases', 'Agent Clicks "Release for Student"')
dot.node('LockReleased', 'Control Returned to Student')

dot.node('LockTimerExpired', 'Lock Expires (48hr)')
dot.node('ExpiryNoticeSent', '6-Hour Warning\nEmail to Agent')
dot.node('StudentTakesBack', 'Student Can Edit Again')

dot.node('StudentRequestsBack', 'Student Clicks "Request Control Back"')
dot.node('AgentNotified', 'Agent Notified via\nEmail + In-App')
dot.node('NoForceHandoff', 'No Forceful Takeover')

# Edges
dot.edge('Start', 'StudentControl')
dot.edge('StudentControl', 'AgentRequests')
dot.edge('AgentRequests', 'AgentControl')
dot.edge('AgentControl', 'AgentViewEditable')
dot.edge('AgentControl', 'StudentViewReadOnly')

dot.edge('AgentViewEditable', 'AgentReleases')
dot.edge('AgentReleases', 'LockReleased')
dot.edge('LockReleased', 'StudentControl')

dot.edge('AgentControl', 'LockTimerExpired', label='(after 48hr)')
dot.edge('LockTimerExpired', 'StudentTakesBack')
dot.edge('LockTimerExpired', 'StudentViewReadOnly')
dot.edge('LockTimerExpired', 'ExpiryNoticeSent')

dot.edge('StudentViewReadOnly', 'StudentRequestsBack')
dot.edge('StudentRequestsBack', 'AgentNotified')
dot.edge('AgentNotified', 'NoForceHandoff')
dot.edge('StudentTakesBack', 'StudentControl')

# Output
dot.render('editing_control_flow', format='png', cleanup=True)

print("Flow diagram generated as 'editing_control_flow.png'")
