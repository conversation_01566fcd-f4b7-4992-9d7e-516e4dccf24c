import graphviz

# Process Flow Diagram
process_flow = graphviz.Digraph(format='png')
process_flow.attr(rankdir='LR', size='8')

# Nodes
process_flow.node('A', 'Video Feeds / Drone Streams')
process_flow.node('B', 'Vertex AI Vision\n(Crowd Density + Flow)')
process_flow.node('C', 'Vertex AI Forecasting\n(Predict Bottlenecks)')
process_flow.node('D', 'Gemini Multimodal\n(Fire/Surge/Anomalies)')
process_flow.node('E', 'Security Reports / App Alerts')
process_flow.node('F', 'Gemini (NLP)\nSummarize Security Concerns')
process_flow.node('G', 'AI Agent\n(Incident Prioritization)')
process_flow.node('H', 'Google Maps + GPS\n(Resource Dispatch)')
process_flow.node('I', 'Firebase Studio Dashboard\n(Alert & Visual Display)')
process_flow.node('J', 'Lost & Found\nImage Search')
process_flow.node('K', 'Gemini Vision\nPhoto Matching Engine')

# Edges
process_flow.edges([
    ('A', 'B'), ('B', 'C'), ('B', 'D'),
    ('C', 'I'), ('D', 'I'),
    ('E', 'F'), ('F', 'I'),
    ('I', 'G'), ('G', 'H'),
    ('J', 'K'), ('K', 'I')
])

# Save as DOT files (source format) since graphviz executable is not available
with open('Process_Flow_Diagram.dot', 'w') as f:
    f.write(process_flow.source)

# Architecture Diagram
architecture = graphviz.Digraph(format='png')
architecture.attr(rankdir='TB', size='8')

architecture.node('Input', 'Data Sources:\n- Video Feeds\n- Drone Streams\n- Security Reports\n- Crowd App')
architecture.node('Vision', 'Vertex AI Vision\n(Crowd Density, Flow)')
architecture.node('Forecast', 'Vertex AI Forecasting')
architecture.node('Gemini', 'Gemini Multimodal + NLP\n(Summary, Panic, Fire)')
architecture.node('Agent', 'Vertex AI Agent\n(Dispatch & Response Engine)')
architecture.node('Maps', 'Google Maps API\n(GPS + Routing)')
architecture.node('Firebase', 'Firebase Studio\n(Dashboard + Alerts)')
architecture.node('Storage', 'Firestore / Cloud Storage')
architecture.node('Functions', 'Cloud Functions\n(Event Logic)')
architecture.node('PubSub', 'Pub/Sub\n(Messaging & Triggers)')

architecture.edges([
    ('Input', 'Vision'), ('Vision', 'Forecast'),
    ('Input', 'Gemini'), ('Forecast', 'Firebase'),
    ('Gemini', 'Firebase'), ('Firebase', 'Agent'),
    ('Agent', 'Maps'), ('Agent', 'Functions'),
    ('Functions', 'Storage'), ('Storage', 'Firebase'),
    ('PubSub', 'Functions'), ('Input', 'PubSub')
])

# Save as DOT files (source format) since graphviz executable is not available
with open('Architecture_Diagram.dot', 'w') as f:
    f.write(architecture.source)

print("Diagrams generated successfully!")
print("Files created:")
print("- Process_Flow_Diagram.dot")
print("- Architecture_Diagram.dot")
print("\nNote: DOT files created. To convert to PNG, install graphviz system package and run:")
print("dot -Tpng Process_Flow_Diagram.dot -o Process_Flow_Diagram.png")
print("dot -Tpng Architecture_Diagram.dot -o Architecture_Diagram.png")
