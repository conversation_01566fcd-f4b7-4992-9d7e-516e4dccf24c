digraph {
	rankdir=TB size=8
	Input [label="Data Sources:
- Video Feeds
- Drone Streams
- Security Reports
- Crowd App"]
	Vision [label="Vertex AI Vision
(Crowd Density, Flow)"]
	Forecast [label="Vertex AI Forecasting"]
	Gemini [label="Gemini Multimodal + NLP
(Summary, Panic, Fire)"]
	Agent [label="Vertex AI Agent
(Dispatch & Response Engine)"]
	Maps [label="Google Maps API
(GPS + Routing)"]
	Firebase [label="Firebase Studio
(Dashboard + Alerts)"]
	Storage [label="Firestore / Cloud Storage"]
	Functions [label="Cloud Functions
(Event Logic)"]
	PubSub [label="Pub/Sub
(Messaging & Triggers)"]
	Input -> Vision
	Vision -> Forecast
	Input -> Gemini
	Forecast -> Firebase
	Gemini -> Firebase
	Firebase -> Agent
	Agent -> Maps
	Agent -> Functions
	Functions -> Storage
	Storage -> Firebase
	PubSub -> Functions
	Input -> PubSub
}
